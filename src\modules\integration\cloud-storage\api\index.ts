import { apiClient } from '@/shared/api/client';
import type { ApiResponseDto } from '@/shared/dto/response/api-response.dto';
import type {
  CloudStorageProviderConfiguration,
  CreateCloudStorageProviderDto,
  UpdateCloudStorageProviderDto,
  TestCloudStorageProviderDto,
  TestCloudStorageProviderWithConfigDto,
  CloudStorageProviderTestResult,
  CloudStorageProviderQueryParams,
  CloudStorageFile,
  CloudStorageFolder,
  FileUploadRequest,
  FileDownloadRequest,
  FolderCreateRequest,
  CloudStorageSyncStatus,
  FileSearchRequest,
  BatchOperationRequest,
  ShareLinkRequest,
  ShareLinkResponse,
} from '../types';

/**
 * Cloud Storage Provider API
 */

// Provider Configuration APIs
export const getCloudStorageProviders = async (params?: CloudStorageProviderQueryParams) => {
  return apiClient.get<ApiResponseDto<CloudStorageProviderConfiguration[]>>('/integration/cloud-storage/providers', { params });
};

export const getCloudStorageProvider = async (id: number) => {
  return apiClient.get<ApiResponseDto<CloudStorageProviderConfiguration>>(`/integration/cloud-storage/providers/${id}`);
};

export const createCloudStorageProvider = async (data: CreateCloudStorageProviderDto) => {
  return apiClient.post<ApiResponseDto<CloudStorageProviderConfiguration>>('/integration/cloud-storage/providers', data);
};

export const updateCloudStorageProvider = async (id: number, data: UpdateCloudStorageProviderDto) => {
  return apiClient.put<ApiResponseDto<CloudStorageProviderConfiguration>>(`/integration/cloud-storage/providers/${id}`, data);
};

export const deleteCloudStorageProvider = async (id: number) => {
  return apiClient.delete<ApiResponseDto<void>>(`/integration/cloud-storage/providers/${id}`);
};

export const testCloudStorageProvider = async (id: number, data?: TestCloudStorageProviderDto) => {
  return apiClient.post<ApiResponseDto<CloudStorageProviderTestResult>>(`/integration/cloud-storage/providers/${id}/test`, data);
};

export const testCloudStorageProviderWithConfig = async (data: TestCloudStorageProviderWithConfigDto) => {
  return apiClient.post<ApiResponseDto<CloudStorageProviderTestResult>>('/integration/cloud-storage/providers/test-config', data);
};

// OAuth APIs
export const getOAuthUrl = async (providerId: number) => {
  return apiClient.get<ApiResponseDto<{ authUrl: string }>>(`/integration/cloud-storage/providers/${providerId}/oauth/url`);
};

export const handleOAuthCallback = async (providerId: number, code: string, state?: string) => {
  return apiClient.post<ApiResponseDto<{ accessToken: string; refreshToken: string }>>(`/integration/cloud-storage/providers/${providerId}/oauth/callback`, {
    code,
    state,
  });
};

// File Management APIs
export const getFiles = async (providerId: number, folderId?: string) => {
  return apiClient.get<ApiResponseDto<CloudStorageFile[]>>(`/integration/cloud-storage/providers/${providerId}/files`, {
    params: { folderId },
  });
};

export const getFolders = async (providerId: number, parentId?: string) => {
  return apiClient.get<ApiResponseDto<CloudStorageFolder[]>>(`/integration/cloud-storage/providers/${providerId}/folders`, {
    params: { parentId },
  });
};

export const uploadFile = async (providerId: number, data: FileUploadRequest) => {
  const formData = new FormData();
  formData.append('fileName', data.fileName);
  formData.append('file', data.fileContent);
  if (data.parentFolderId) formData.append('parentFolderId', data.parentFolderId);
  if (data.description) formData.append('description', data.description);
  if (data.mimeType) formData.append('mimeType', data.mimeType);

  return apiClient.post<ApiResponseDto<CloudStorageFile>>(`/integration/cloud-storage/providers/${providerId}/files/upload`, formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
};

export const downloadFile = async (providerId: number, data: FileDownloadRequest) => {
  return apiClient.get<Blob>(`/integration/cloud-storage/providers/${providerId}/files/${data.fileId}/download`, {
    responseType: 'blob',
  });
};

export const deleteFile = async (providerId: number, fileId: string) => {
  return apiClient.delete<ApiResponseDto<void>>(`/integration/cloud-storage/providers/${providerId}/files/${fileId}`);
};

export const createFolder = async (providerId: number, data: FolderCreateRequest) => {
  return apiClient.post<ApiResponseDto<CloudStorageFolder>>(`/integration/cloud-storage/providers/${providerId}/folders`, data);
};

export const deleteFolder = async (providerId: number, folderId: string) => {
  return apiClient.delete<ApiResponseDto<void>>(`/integration/cloud-storage/providers/${providerId}/folders/${folderId}`);
};

// Search APIs
export const searchFiles = async (providerId: number, data: FileSearchRequest) => {
  return apiClient.post<ApiResponseDto<CloudStorageFile[]>>(`/integration/cloud-storage/providers/${providerId}/search`, data);
};

// Batch Operations
export const batchOperation = async (providerId: number, data: BatchOperationRequest) => {
  return apiClient.post<ApiResponseDto<{ success: boolean; results: any[] }>>(`/integration/cloud-storage/providers/${providerId}/batch`, data);
};

// Share APIs
export const createShareLink = async (providerId: number, data: ShareLinkRequest) => {
  return apiClient.post<ApiResponseDto<ShareLinkResponse>>(`/integration/cloud-storage/providers/${providerId}/share`, data);
};

export const revokeShareLink = async (providerId: number, fileId: string) => {
  return apiClient.delete<ApiResponseDto<void>>(`/integration/cloud-storage/providers/${providerId}/share/${fileId}`);
};

// Sync APIs
export const syncProvider = async (providerId: number) => {
  return apiClient.post<ApiResponseDto<CloudStorageSyncStatus>>(`/integration/cloud-storage/providers/${providerId}/sync`);
};

export const getSyncStatus = async (providerId: number) => {
  return apiClient.get<ApiResponseDto<CloudStorageSyncStatus>>(`/integration/cloud-storage/providers/${providerId}/sync/status`);
};

// Storage Quota APIs
export const getStorageQuota = async (providerId: number) => {
  return apiClient.get<ApiResponseDto<{ totalBytes: number; usedBytes: number; availableBytes: number }>>(`/integration/cloud-storage/providers/${providerId}/quota`);
};
